<template>
  <div class="authentication-container">
    <!-- 顶部导航切换 - 只有两个权限都有时才显示 -->
    <div v-if="showNavigation" class="auth-nav">
      <div class="nav-tab" :class="{ active: activeTab === 'enterprise' }" @click="switchTab('enterprise')"> 企业认证 </div>
      <div class="nav-tab" :class="{ active: activeTab === 'personal' }" @click="switchTab('personal')"> 个人认证 </div>
    </div>

    <!-- 内容区域 -->
    <div class="auth-content">
      <!-- 无权限提示 -->
      <div v-if="!hasEnterprisePermission && !hasPersonalPermission" class="no-permission">
        <a-empty description="您没有认证相关权限，请联系管理员" />
      </div>

      <!-- 企业认证表单 -->
      <div v-if="activeTab === 'enterprise' && hasEnterprisePermission" class="form-section">
        <!-- 企业名称 -->
        <div class="form-item">
          <label class="form-label">企业名称</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.enterpriseName" placeholder="请输入企业名称" class="form-input" />
          </div>
        </div>

        <!-- 统一社会信用代码 -->
        <div class="form-item">
          <label class="form-label">统一社会信用代码</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.creditCode" placeholder="请输入统一社会信用代码" class="form-input" />
          </div>
        </div>

        <!-- 业务联系人 -->
        <div class="form-item">
          <label class="form-label">业务联系人</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.relationUser" placeholder="请输入业务联系人" class="form-input" />
          </div>
        </div>

        <!-- 联系电话 -->
        <div class="form-item">
          <label class="form-label">联系电话</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.relationPhone" placeholder="请输入联系电话" class="form-input" />
          </div>
        </div>

        <!-- 法人真实姓名 -->
        <div class="form-item">
          <label class="form-label">法人真实姓名</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.legalName" placeholder="请输入法人真实姓名" class="form-input" />
          </div>
        </div>

        <!-- 法人证件类型 -->
        <div class="form-item">
          <label class="form-label">法人证件类型</label>
          <div class="form-value">
            <a-radio-group v-model:value="enterpriseForm.cartType">
              <a-radio :value="1">身份证</a-radio>
              <!-- <a-radio :value="2">其他</a-radio> -->
            </a-radio-group>
          </div>
        </div>

        <!-- 法人证件号 -->
        <div class="form-item">
          <label class="form-label">法人证件号</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.cartId" placeholder="请输入法人证件号" class="form-input" />
          </div>
        </div>

        <!-- 营业执照照片 -->
        <div class="form-item">
          <label class="form-label">营业执照照片</label>
          <div class="form-value">
            <JUpload
              v-model:value="businessLicenseAttachments"
              :maxCount="1"
              file-type="image"
              bizPath="temp"
              text="上传营业执照"
              :return-url="false"
            />
          </div>
        </div>

        <!-- 身份证正反面照片 -->
        <div class="form-item">
          <label class="form-label">身份证正反面照片</label>
          <div class="form-value">
            <JUpload
              v-model:value="enterpriseIdCardAttachments"
              :maxCount="2"
              file-type="image"
              bizPath="temp"
              text="上传身份证照片"
              :return-url="false"
            />
          </div>
        </div>

        <!-- 企业logo -->
        <div class="form-item">
          <label class="form-label">企业logo</label>
          <div class="form-value">
            <JUpload v-model:value="enterpriseForm.companyLogo" :maxCount="1" file-type="image" bizPath="temp" text="上传企业logo" />
          </div>
        </div>

        <!-- 企业简介 -->
        <div class="form-item">
          <label class="form-label">企业简介</label>
          <div class="form-value">
            <a-textarea v-model:value="enterpriseForm.description" placeholder="请输入企业简介" :rows="4" class="form-input" />
          </div>
        </div>
      </div>

      <!-- 个人认证表单 -->
      <div v-if="activeTab === 'personal' && hasPersonalPermission" class="form-section">
        <!-- 真实姓名 -->
        <div class="form-item">
          <label class="form-label">真实姓名</label>
          <div class="form-value">
            <a-input v-model:value="personalForm.name" placeholder="请输入真实姓名" class="form-input" />
          </div>
        </div>

        <!-- 手机号 -->
        <div class="form-item">
          <label class="form-label">手机号</label>
          <div class="form-value">
            <a-input v-model:value="personalForm.phone" placeholder="请输入手机号" class="form-input" />
          </div>
        </div>

        <!-- 证件类型 -->
        <div class="form-item">
          <label class="form-label">证件类型</label>
          <div class="form-value">
            <a-radio-group v-model:value="personalForm.cartType">
              <a-radio :value="1">身份证</a-radio>
              <!-- <a-radio :value="2">其他</a-radio> -->
            </a-radio-group>
          </div>
        </div>

        <!-- 证件号 -->
        <div class="form-item">
          <label class="form-label">证件号</label>
          <div class="form-value">
            <a-input v-model:value="personalForm.cartId" placeholder="请输入证件号" class="form-input" />
          </div>
        </div>

        <!-- 身份证正反面照片 -->
        <div class="form-item">
          <label class="form-label">身份证正反面照片</label>
          <div class="form-value">
            <JUpload
              v-model:value="personalIdCardAttachments"
              :maxCount="2"
              file-type="image"
              bizPath="temp"
              text="上传身份证照片"
              :return-url="false"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 - 只有有权限时才显示 -->
    <div v-if="hasEnterprisePermission || hasPersonalPermission" class="action-buttons">
      <!-- <a-button class="save-btn" @click="handleSave" :loading="saveLoading">保存</a-button> -->
      <a-button type="primary" class="submit-btn" @click="handleSubmitPrev" :loading="submitLoading">提交审核</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { JUpload } from '/@/components/Form/src/jeecg/components/JUpload';
  import { useUserStore } from '/@/store/modules/user';
  import { usePermission } from '/@/hooks/web/usePermission';
  import {
    PersonalAuthData,
    EnterpriseAuthData,
    AttachmentItem,
    submitPersonalAuth,
    submitEnterpriseAuth,
    getPersonalAuth,
    getEnterpriseAuth,
  } from '/@/api/system/userAuth';
  // 引入防重复提交工具
  import { usePreventDuplicateSubmit } from '@/utils/preventDuplicateSubmit';

  // 初始化防重复提交功能
  const { executeSubmit } = usePreventDuplicateSubmit({
    interval: 2000, // 防重复提交间隔2秒
    showMessage: false, // 不显示默认提示，因为项目中封装好的请求已经有弹窗了
    autoReset: true, // 自动重置状态
    resetDelay: 1500, // 1.5秒后重置状态
    enableDebounce: true, // 启用防抖
    debounceDelay: 300, // 防抖延迟300ms
  });

  const saveLoading = ref(false);
  const submitLoading = ref(false);

  const userStore = useUserStore();
  const { hasPermission } = usePermission();

  // 权限定义
  const ENTERPRISE_AUTH_PERMISSION = 'hgy.personalCenter:hgy_enterprise_auth:addOrUpdateEnterpriseAuth';
  const PERSONAL_AUTH_PERMISSION = 'hgy_personal_auth:addOrUpdatePersonalAuth';

  // 权限检查
  const hasEnterprisePermission = computed(() => hasPermission(ENTERPRISE_AUTH_PERMISSION));
  const hasPersonalPermission = computed(() => hasPermission(PERSONAL_AUTH_PERMISSION));
  const showNavigation = computed(() => hasEnterprisePermission.value && hasPersonalPermission.value);

  // 当前激活的标签页 - 根据权限设置默认值
  const getDefaultTab = (): 'enterprise' | 'personal' => {
    if (hasEnterprisePermission.value && hasPersonalPermission.value) {
      return 'enterprise'; // 两个权限都有，默认显示企业认证
    } else if (hasEnterprisePermission.value) {
      return 'enterprise'; // 只有企业认证权限
    } else if (hasPersonalPermission.value) {
      return 'personal'; // 只有个人认证权限
    } else {
      return 'enterprise'; // 没有权限时默认企业认证（虽然不会显示）
    }
  };

  const activeTab = ref<'enterprise' | 'personal'>(getDefaultTab());

  // 企业认证表单数据
  const enterpriseForm = reactive<Partial<EnterpriseAuthData>>({
    enterpriseName: '',
    creditCode: '',
    relationUser: '',
    relationPhone: '',
    legalName: '',
    cartType: 1,
    cartId: '',
    companyLogo: '',
    description: '',
    review: 1,
  });

  // 个人认证表单数据
  const personalForm = reactive<Partial<PersonalAuthData>>({
    name: '',
    phone: '',
    cartType: 1,
    cartId: '',
    review: 1,
  });

  // 企业认证附件
  const businessLicenseAttachments = ref<string>(''); // 营业执照附件
  const enterpriseIdCardAttachments = ref<string>(''); // 企业身份证附件
  // 个人认证附件
  const personalIdCardAttachments = ref<string>(''); // 个人身份证附件

  // 获取用户ID
  const userId = computed(() => userStore.getUserInfo?.id);

  /**
   * 切换标签页
   */
  const switchTab = (tab: 'enterprise' | 'personal') => {
    // 检查权限
    if (tab === 'enterprise' && !hasEnterprisePermission.value) {
      message.warning('您没有企业认证权限');
      return;
    }
    if (tab === 'personal' && !hasPersonalPermission.value) {
      message.warning('您没有个人认证权限');
      return;
    }

    activeTab.value = tab;
    loadAuthData();
  };

  /**
   * 加载认证数据
   */
  const loadAuthData = async () => {
    console.log('加载认证数据', userStore.getUserInfo);
    if (!userId.value) {
      console.warn('用户ID不存在，无法加载认证数据');
      return;
    }

    try {
      if (activeTab.value === 'enterprise') {
        const response = await getEnterpriseAuth(userId.value);
        console.log('企业认证数据:', response);

        if (response) {
          // 处理企业认证数据结构
          if (response.hgyEnterpriseAuth) {
            // 如果返回的是包装结构，提取 hgyEnterpriseAuth 数据
            const authData = response.hgyEnterpriseAuth;
            Object.assign(enterpriseForm, {
              enterpriseName: authData.enterpriseName,
              creditCode: authData.creditCode,
              relationUser: authData.relationUser,
              relationPhone: authData.relationPhone,
              legalName: authData.legalName,
              cartType: authData.cartType,
              cartId: authData.cartId,
              companyLogo: authData.companyLogo,
              description: authData.description,
              review: 1,
            });
          } else {
            // 如果返回的是直接的认证数据
            Object.assign(enterpriseForm, response);
          }

          // 处理附件数据
          const attachmentList = response.hgyAttachmentList;
          if (attachmentList && attachmentList.length > 0) {
            processEnterpriseAttachmentList(attachmentList);
          }
        }
      } else {
        const response = await getPersonalAuth(userId.value);
        console.log('个人认证数据:', response);

        if (response) {
          // 处理个人认证数据结构
          if (response.hgyPersonalAuth) {
            // 如果返回的是包装结构，提取个人认证数据
            const authData = response.hgyPersonalAuth;
            Object.assign(personalForm, {
              name: authData.name,
              phone: authData.phone,
              cartType: authData.cartType,
              cartId: authData.cartId,
              review: 1,
            });
          } else {
            // 如果返回的是直接的认证数据
            Object.assign(personalForm, response);
          }

          // 处理附件数据
          const attachmentList = response.hgyAttachmentList;
          if (attachmentList && attachmentList.length > 0) {
            processPersonalAttachmentList(attachmentList);
          }
        }
      }
    } catch (error) {
      console.error('加载认证数据失败:', error);
    }
  };

  /**
   * 处理企业认证附件列表回显
   */
  const processEnterpriseAttachmentList = (attachmentList: any[]) => {
    const businessLicense: any[] = [];
    const idCards: any[] = [];

    attachmentList.forEach((item) => {
      const fileData = {
        fileName: item.fileName,
        filePath: item.filePath,
        fileSize: item.fileSize,
        fileType: item.fileType,
      };

      // 根据 bizType 判断是营业执照还是身份证
      if (item.bizType === 'YYZZ') {
        businessLicense.push(fileData);
      } else if (item.bizType === 'SFZ') {
        idCards.push(fileData);
      }
    });

    // 更新表单数据 - JUpload 组件期望接收 JSON 字符串格式
    if (businessLicense.length > 0) {
      businessLicenseAttachments.value = JSON.stringify(businessLicense);
    }
    if (idCards.length > 0) {
      enterpriseIdCardAttachments.value = JSON.stringify(idCards);
    }
  };

  /**
   * 处理个人认证附件列表回显
   */
  const processPersonalAttachmentList = (attachmentList: any[]) => {
    const idCards: any[] = [];

    attachmentList.forEach((item) => {
      if (item.bizType === 'SFZ') {
        const fileData = {
          fileName: item.fileName,
          filePath: item.filePath,
          fileSize: item.fileSize,
          fileType: item.fileType,
        };
        idCards.push(fileData);
      }
    });

    // 更新表单数据
    if (idCards.length > 0) {
      personalIdCardAttachments.value = JSON.stringify(idCards);
    }
  };

  /**
   * 处理附件数据 - 将JUpload组件的数据转换为提交格式
   */
  const processAttachmentsForSubmit = (businessLicenseStr: string, idCardStr: string): AttachmentItem[] => {
    const attachments: AttachmentItem[] = [];

    // 处理营业执照附件
    if (businessLicenseStr) {
      try {
        const businessLicenseFiles = typeof businessLicenseStr === 'string' ? JSON.parse(businessLicenseStr) : businessLicenseStr;
        const businessLicenseList = Array.isArray(businessLicenseFiles) ? businessLicenseFiles : [];
        businessLicenseList.forEach((file: any) => {
          attachments.push({
            bizType: 'YYZZ',
            fileName: file.fileName,
            filePath: file.filePath,
            fileSize: file.fileSize,
            fileType: file.fileType || 'image',
          });
        });
      } catch (e) {
        console.warn('解析营业执照附件数据失败:', e);
      }
    }

    // 处理身份证附件
    if (idCardStr) {
      try {
        const idCardFiles = typeof idCardStr === 'string' ? JSON.parse(idCardStr) : idCardStr;
        const idCardList = Array.isArray(idCardFiles) ? idCardFiles : [];
        idCardList.forEach((file: any) => {
          attachments.push({
            bizType: 'SFZ',
            fileName: file.fileName,
            filePath: file.filePath,
            fileSize: file.fileSize,
            fileType: file.fileType || 'image',
          });
        });
      } catch (e) {
        console.warn('解析身份证附件数据失败:', e);
      }
    }

    return attachments;
  };

  /**
   * 处理个人认证附件数据
   */
  const processPersonalAttachmentsForSubmit = (idCardStr: string): AttachmentItem[] => {
    if (!idCardStr) return [];

    try {
      const idCardFiles = typeof idCardStr === 'string' ? JSON.parse(idCardStr) : idCardStr;
      const idCardList = Array.isArray(idCardFiles) ? idCardFiles : [];
      return idCardList.map((file: any) => ({
        bizType: 'SFZ',
        fileName: file.fileName,
        filePath: file.filePath,
        fileSize: file.fileSize,
        fileType: file.fileType || 'image',
      }));
    } catch (e) {
      console.warn('解析个人身份证附件数据失败:', e);
      return [];
    }
  };

  /**
   * 处理保存操作
   */
  const handleSave = async () => {
    if (!userId.value) {
      message.warning('用户信息异常，请重新登录');
      return;
    }

    // 权限检查
    if (activeTab.value === 'enterprise' && !hasEnterprisePermission.value) {
      message.warning('您没有企业认证权限');
      return;
    }
    if (activeTab.value === 'personal' && !hasPersonalPermission.value) {
      message.warning('您没有个人认证权限');
      return;
    }

    saveLoading.value = true;
    try {
      if (activeTab.value === 'enterprise') {
        const submitData: EnterpriseAuthData = {
          ...(enterpriseForm as EnterpriseAuthData),
          userId: String(userId.value),
          attachmentList: processAttachmentsForSubmit(businessLicenseAttachments.value, enterpriseIdCardAttachments.value),
        };
        await submitEnterpriseAuth(submitData);
      } else {
        const submitData: PersonalAuthData = {
          ...(personalForm as PersonalAuthData),
          userId: String(userId.value),
          attachmentList: processPersonalAttachmentsForSubmit(personalIdCardAttachments.value),
        };
        await submitPersonalAuth(submitData);
      }
    } catch (error) {
      console.error('保存失败:', error);
    } finally {
      saveLoading.value = false;
    }
  };

  /**
   * 处理提交审核前操作
   */
  const handleSubmitPrev = async () => {
    // 使用防重复提交功能执行提交操作
    const result = await executeSubmit(async () => {
      return await handleSubmit();
    }, 'submit');

    // 如果返回null，说明被防重复提交拦截了
    if (result === null) {
      return;
    }
  };

  /**
   * 处理提交审核操作
   */
  const handleSubmit = async () => {
    if (!userId.value) {
      message.warning('用户信息异常，请重新登录');
      return;
    }

    // 权限检查
    if (activeTab.value === 'enterprise' && !hasEnterprisePermission.value) {
      message.warning('您没有企业认证权限');
      return;
    }
    if (activeTab.value === 'personal' && !hasPersonalPermission.value) {
      message.warning('您没有个人认证权限');
      return;
    }

    // 表单验证
    if (activeTab.value === 'enterprise') {
      if (!enterpriseForm.enterpriseName || !enterpriseForm.creditCode || !enterpriseForm.legalName) {
        message.warning('请填写完整的企业信息');
        return;
      }
    } else {
      if (!personalForm.name || !personalForm.phone || !personalForm.cartId) {
        message.warning('请填写完整的个人信息');
        return;
      }
    }

    submitLoading.value = true;
    try {
      if (activeTab.value === 'enterprise') {
        const submitData: EnterpriseAuthData = {
          ...(enterpriseForm as EnterpriseAuthData),
          userId: String(userId.value),
          attachmentList: processAttachmentsForSubmit(businessLicenseAttachments.value, enterpriseIdCardAttachments.value),
        };
        await submitEnterpriseAuth(submitData);
      } else {
        const submitData: PersonalAuthData = {
          ...(personalForm as PersonalAuthData),
          userId: String(userId.value),
          attachmentList: processPersonalAttachmentsForSubmit(personalIdCardAttachments.value),
        };
        await submitPersonalAuth(submitData);
      }
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      submitLoading.value = false;
    }
  };

  onMounted(() => {
    loadAuthData();
  });
</script>

<style scoped lang="less">
  .authentication-container {
    .auth-nav {
      display: flex;
      height: 55px;
      width: 100%;
      border-radius: 10px;

      .nav-tab {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: #eee;
        color: #999;

        &.active {
          background-color: #fff;
          color: #004c66;
        }

        &:hover:not(.active) {
          background-color: #e0e0e0;
        }

        &:first-child {
          border-top-left-radius: 10px;
        }

        &:last-child {
          border-top-right-radius: 10px;
        }
      }
    }

    .auth-content {
      padding: 20px;
      min-height: 680px;
      position: relative;

      .no-permission {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 400px;
      }

      &::before {
        content: '';
        position: absolute;
        bottom: 20px;
        width: calc(100% - 40px);
        height: 1px;
        background: linear-gradient(to right, rgba(221, 221, 221, 1), rgba(255, 255, 255, 1));
      }

      .form-section {
        .form-item {
          padding: 20px 0;
          border-bottom: 1px solid;
          border-image: linear-gradient(to right, rgba(221, 221, 221, 1), rgba(255, 255, 255, 1));
          border-image-slice: 1;

          .form-label {
            font-size: 16px;
            color: #333;
            flex-shrink: 0;
            font-family: 'PingFang Bold';
            margin-bottom: 10px;
            display: block;
          }

          .form-value {
            flex: 1;

            .form-input {
              width: 100%;
              border: none;
              outline: none;
              font-size: 16px;
              color: #666;
              background: transparent;
              transition: border-color 0.3s ease;

              &::placeholder {
                color: #999;
                font-size: 16px;
              }

              &:focus {
                border-bottom-color: #004c66;
              }
            }

            :deep(.ant-input) {
              border: none;
              outline: none;
              font-size: 16px;
              color: #666;
              background: transparent;
              box-shadow: none;
              padding: 0;

              &::placeholder {
                color: #999;
                font-size: 16px;
              }

              &:focus {
                border-bottom-color: #004c66;
                box-shadow: none;
              }
            }

            :deep(.ant-input-affix-wrapper) {
              border: none;
              background: transparent;
              box-shadow: none;
              padding: 0;

              &:focus,
              &:focus-within {
                border-bottom-color: #004c66;
                box-shadow: none;
              }
            }

            :deep(.ant-radio-group) {
              .ant-radio-wrapper {
                color: #666;
                font-size: 16px;
              }
            }

            :deep(.ant-upload-wrapper) {
              .ant-upload-list {
                margin-top: 10px;
              }
            }
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      padding: 0 20px 20px 20px;

      .save-btn,
      .submit-btn {
        border-radius: 6px;
        font-size: 16px;
        min-width: 120px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .save-btn {
        background-color: rgba(0, 76, 102, 0.2);
        color: #004c66;
        border: 1px solid rgba(0, 76, 102, 0.3);

        &:hover {
          background-color: rgba(0, 76, 102, 0.3);
          border-color: rgba(0, 76, 102, 0.4);
        }
      }

      .submit-btn {
        background-color: #004c66;
        border-color: #004c66;

        &:hover {
          background-color: #003a4d;
          border-color: #003a4d;
        }
      }
    }
  }
</style>
