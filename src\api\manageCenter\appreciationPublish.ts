import { defHttp } from '/@/utils/http/axios';

// 已通过审核的委托单项
export interface PassedReviewItem {
  id: string;
  entrustOrderNo: string; // 委托单号
  entrustCompanyName: string; // 委托单位名称
  serviceType: number; // 服务类型
  createTime: string; // 创建时间
}

// 平台更新参数
export interface PlatformUpdateParams {
  id?: string; // 编辑时传入
  entrustOrderId: string; // 关联委托单号
  entrustType: number; // 委托类型(1-增值 2-自主)
  serviceType: number; // 服务类型(2-资产处置 3-采购信息)
  status: number; // 状态(1-草稿 2-提交)

  // 资产处置相关字段
  entrustCompanyId?: string; // 处置单位ID
  onEntrustCompanyId?: string; // 委托企业ID
  assetName?: string; // 资产名称
  assetNo?: string; // 资产编号
  assetType?: number; // 资产类型（兼容旧版本）
  materialTypeOne?: string; // 资产类型第一级
  materialTypeTwo?: string; // 资产类型第二级
  materialTypeThree?: string; // 资产类型第三级
  quantity?: string; // 资产数量
  unit?: string; // 计量单位
  quantityFlag?: number; // 是否展示实际数量 0-否 1-是
  servicePayType?: number; // 支付方式 1-买方支付 2-卖方支付
  serviceLife?: number; // 使用年限
  depreciationDegree?: number; // 新旧程度
  currentStatus?: number; // 当前状态
  appraisalValue?: number; // 评估价值
  disposalPrice?: number; // 处置底价
  disposalStartTime?: string; // 处置开始时间
  disposalEndTime?: string; // 处置结束时间
  paymentMethod?: number; // 付款方式
  isTaxIncluded?: string; // 是否含税
  provinceCode?: string; // 省份代码
  cityCode?: string; // 城市代码
  districtCode?: string; // 区域代码
  address?: string; // 详细地址
  specialNotes?: string; // 特殊说明
  relationUser?: string; // 联系人
  relationPhone?: string; // 联系电话
  coverImage?: string; // 封面图片
  attachmentList?: any[]; // 附件列表

  // 采购信息相关字段
  noticeName?: string; // 公告名称
}

/**
 * 查询已通过审核的委托单
 */
export const queryPassedReview = () => {
  return defHttp.get<PassedReviewItem[]>({
    url: '/hgy/entrustService/hgyEntrustOrder/workbench/queryPassedReview',
  });
};

/**
 * 平台更新委托信息
 */
export const platformUpdate = (params: PlatformUpdateParams) => {
  return defHttp.post({
    url: '/hgy/entrustService/hgyAssetEntrust/platformUpdate',
    params,
  });
};

/**
 * 根据委托单号获取资产处置详情
 * @param params 查询参数
 */
export const queryEntrustById = (params: { id: string }) => {
  return defHttp.get({ url: '/hgy/entrustService/hgyAssetEntrust/queryEntrustById', params });
};

/**
 * 根据委托单号获取采购信息详情
 * @param params 查询参数
 */
export const queryProcurementById = (params: { id: string }) => {
  return defHttp.get({ url: '/hgy/entrustService/hgyProcurement/queryProcurementById', params });
};

/**
 * 采购信息平台更新接口
 * @param params 更新参数
 */
export const platformUpdateProcurement = (params: PlatformUpdateParams) => {
  return defHttp.post({
    url: '/hgy/entrustService/hgyProcurement/platformUpdate',
    params,
  });
};
