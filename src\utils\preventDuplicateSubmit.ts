import { ref, Ref } from 'vue';
import { message } from 'ant-design-vue';

/**
 * 防重复提交配置接口
 */
export interface PreventDuplicateConfig {
  /** 防重复提交的时间间隔，单位毫秒，默认3000ms */
  interval?: number;
  /** 是否显示重复提交提示，默认true */
  showMessage?: boolean;
  /** 自定义重复提交提示文本 */
  messageText?: string;
  /** 是否在提交成功后自动重置状态，默认true */
  autoReset?: boolean;
  /** 自动重置的延迟时间，单位毫秒，默认1500ms */
  resetDelay?: number;
  /** 是否启用防抖，默认true */
  enableDebounce?: boolean;
  /** 防抖延迟时间，单位毫秒，默认300ms */
  debounceDelay?: number;
}

/**
 * 防重复提交返回值接口
 */
export interface PreventDuplicateReturn {
  /** 是否正在提交中 */
  isSubmitting: Ref<boolean>;
  /** 执行提交操作 */
  executeSubmit: <T = any>(submitFn: () => Promise<T>, actionType?: string) => Promise<T | null>;
  /** 手动重置提交状态 */
  resetSubmitState: () => void;
  /** 设置提交状态 */
  setSubmitting: (status: boolean) => void;
  /** 检查是否可以提交 */
  canSubmit: () => boolean;
}

/**
 * 防重复提交组合式函数
 * @param config 配置选项
 * @returns 防重复提交相关方法和状态
 */
export function usePreventDuplicateSubmit(config: PreventDuplicateConfig = {}): PreventDuplicateReturn {
  const {
    interval = 3000,
    showMessage = true,
    messageText = '请勿重复提交，请稍后再试',
    autoReset = true,
    resetDelay = 1500,
    enableDebounce = true,
    debounceDelay = 300
  } = config;

  const isSubmitting = ref(false);
  let lastSubmitTime = 0;
  let resetTimer: NodeJS.Timeout | null = null;
  let debounceTimer: NodeJS.Timeout | null = null;
  let currentActionType = '';

  /**
   * 检查是否可以提交
   */
  function canSubmit(): boolean {
    const currentTime = Date.now();
    
    // 检查是否在提交中
    if (isSubmitting.value) {
      return false;
    }

    // 检查时间间隔
    if (currentTime - lastSubmitTime < interval) {
      return false;
    }

    return true;
  }

  /**
   * 清除所有定时器
   */
  function clearTimers(): void {
    if (resetTimer) {
      clearTimeout(resetTimer);
      resetTimer = null;
    }
    if (debounceTimer) {
      clearTimeout(debounceTimer);
      debounceTimer = null;
    }
  }

  /**
   * 执行提交操作
   * @param submitFn 提交函数
   * @param actionType 操作类型（如：'save'、'submit'等）
   * @returns Promise<T | null>
   */
  async function executeSubmit<T = any>(
    submitFn: () => Promise<T>, 
    actionType: string = 'submit'
  ): Promise<T | null> {
    return new Promise((resolve) => {
      // 清除之前的防抖定时器
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      // 防抖处理
      if (enableDebounce) {
        debounceTimer = setTimeout(async () => {
          const result = await performSubmit(submitFn, actionType);
          resolve(result);
        }, debounceDelay);
      } else {
        performSubmit(submitFn, actionType).then(resolve);
      }
    });
  }

  /**
   * 执行实际的提交操作
   */
  async function performSubmit<T = any>(
    submitFn: () => Promise<T>, 
    actionType: string
  ): Promise<T | null> {
    const currentTime = Date.now();

    // 检查是否可以提交
    if (!canSubmit()) {
      if (showMessage) {
        // 如果是相同操作类型的重复提交，显示防重复提示
        if (currentActionType === actionType) {
          message.warning(messageText);
        } else {
          // 如果是不同操作类型，显示更具体的提示
          message.warning('请等待当前操作完成后再进行其他操作');
        }
      }
      return null;
    }

    // 清除之前的重置定时器
    clearTimers();

    try {
      // 设置提交状态
      isSubmitting.value = true;
      lastSubmitTime = currentTime;
      currentActionType = actionType;

      console.log(`[防重复提交] 开始执行${actionType}操作`);

      // 执行提交函数
      const result = await submitFn();

      console.log(`[防重复提交] ${actionType}操作执行成功`);

      // 如果配置了自动重置，则延迟重置状态
      if (autoReset) {
        resetTimer = setTimeout(() => {
          isSubmitting.value = false;
          currentActionType = '';
          resetTimer = null;
          console.log(`[防重复提交] ${actionType}操作状态已重置`);
        }, resetDelay);
      }

      return result;
    } catch (error) {
      // 提交失败时立即重置状态
      console.error(`[防重复提交] ${actionType}操作执行失败:`, error);
      isSubmitting.value = false;
      currentActionType = '';
      throw error;
    }
  }

  /**
   * 手动重置提交状态
   */
  function resetSubmitState(): void {
    console.log('[防重复提交] 手动重置提交状态');
    clearTimers();
    isSubmitting.value = false;
    lastSubmitTime = 0;
    currentActionType = '';
  }

  /**
   * 设置提交状态
   * @param status 提交状态
   */
  function setSubmitting(status: boolean): void {
    isSubmitting.value = status;
    if (!status) {
      lastSubmitTime = 0;
      currentActionType = '';
    }
  }

  return {
    isSubmitting,
    executeSubmit,
    resetSubmitState,
    setSubmitting,
    canSubmit
  };
}

/**
 * 防重复提交装饰器（用于类方法）
 * @param config 配置选项
 * @returns 方法装饰器
 */
export function preventDuplicateSubmit(config: PreventDuplicateConfig = {}) {
  const {
    interval = 3000,
    showMessage = true,
    messageText = '请勿重复提交，请稍后再试'
  } = config;

  let lastSubmitTime = 0;
  let isSubmitting = false;

  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const currentTime = Date.now();

      // 检查是否在提交中
      if (isSubmitting) {
        if (showMessage) {
          message.warning(messageText);
        }
        return null;
      }

      // 检查时间间隔
      if (currentTime - lastSubmitTime < interval) {
        if (showMessage) {
          message.warning(messageText);
        }
        return null;
      }

      try {
        isSubmitting = true;
        lastSubmitTime = currentTime;

        const result = await originalMethod.apply(this, args);

        // 延迟重置状态
        setTimeout(() => {
          isSubmitting = false;
        }, 1500);

        return result;
      } catch (error) {
        isSubmitting = false;
        throw error;
      }
    };

    return descriptor;
  };
}

export default usePreventDuplicateSubmit;
